<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volume Shader BM Test Website - Online GPU-Leistungsbewertungstool</title>
    <meta name="description" content="Professionelles Online-GPU-Leistungstesttool, das die Grafikkartenleistung durch komplexes 3D-Volumen-Shader-Rendering testet, mit Unterstützung für Echtzeit-FPS-Überwachung und Leistungsanalyse.">
    <meta name="keywords" content="Grafikkartentest,GPU-Test,Toxic Mushroom Test,volumeshader,WebGL,FPS-Test">
    <meta name="author" content="Toxic Mushroom Test Team">
    <link rel="canonical" href="https://volumeshaderbmtest.com/de-de/">
    <link rel="icon" href="../logo.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="../logo.svg">
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://volumeshaderbmtest.com/de-de/">
    <meta property="og:title" content="Volume Shader BM Test | Online GPU-Leistungsbewertungstool">
    <meta property="og:description" content="Professionelles Online-GPU-Leistungstesttool, das die Grafikkartenleistung durch komplexes 3D-Volumen-Shader-Rendering testet, mit Unterstützung für Echtzeit-FPS-Überwachung und Leistungsanalyse.">
    <meta property="og:image" content="https://volumeshaderbmtest.com/images/og-image.avif">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://volumeshaderbmtest.com/de-de/">
    <meta property="twitter:title" content="Volume Shader BM Test | Online GPU-Leistungsbewertungstool">
    <meta property="twitter:description" content="Professionelles Online-GPU-Leistungstesttool, das die Grafikkartenleistung durch komplexes 3D-Volumen-Shader-Rendering testet, mit Unterstützung für Echtzeit-FPS-Überwachung und Leistungsanalyse.">
    <meta property="twitter:image" content="https://volumeshaderbmtest.com/images/og-img.avif">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "WebSite",
                "@id": "https://volumeshaderbmtest.com/#website",
                "url": "https://volumeshaderbmtest.com/de-de/",
                "name": "Volume Shader BM Test",
                "description": "Professionelles Online-GPU-Leistungsbewertungstool",
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "potentialAction": [
                    {
                        "@type": "SearchAction",
                        "target": {
                            "@type": "EntryPoint",
                            "urlTemplate": "https://volumeshaderbmtest.com/?search={search_term_string}"
                        },
                        "query-input": "required name=search_term_string"
                    }
                ]
            },
            {
                "@type": "Organization",
                "@id": "https://volumeshaderbmtest.com/#organization",
                "name": "Toxic Mushroom Test Team",
                "url": "https://volumeshaderbmtest.com/de-de/",
                "logo": {
                    "@type": "ImageObject",
                    "url": "https://volumeshaderbmtest.com/images/logo.png"
                }
            },
            {
                "@type": "SoftwareApplication",
                "@id": "https://volumeshaderbmtest.com/#software",
                "name": "Volume Shader BM Test",
                "applicationCategory": "UtilitiesApplication",
                "operatingSystem": "Web Browser",
                "url": "https://volumeshaderbmtest.com/de-de/",
                "description": "WebGL-basiertes Online-GPU-Leistungsbewertungstool, das vier Testmodi unterstützt: leicht, mittel, schwer und extrem, mit Echtzeit-Überwachung von FPS, Temperatur und anderen wichtigen Leistungskennzahlen.",
                "featureList": [
                    "GPU-Leistungstest",
                    "Echtzeit-FPS-Überwachung",
                    "Temperaturstatusüberwachung",
                    "Leistungsverlaufsaufzeichnung",
                    "Testergebnisanalyse",
                    "WebGL-Volumenrendering",
                    "Mehrere Schwierigkeits-Testmodi"
                ],
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "CNY",
                    "availability": "https://schema.org/InStock"
                },
                "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": "4.8",
                    "reviewCount": "1250",
                    "bestRating": "5",
                    "worstRating": "1"
                }
            },
            {
                "@type": "Article",
                "@id": "https://volumeshaderbmtest.com/#gpu-knowledge",
                "headline": "GPU-Wissensdatenbank - Vollständiger technischer Leitfaden für Grafikkarten",
                "description": "Detaillierte Einführung in GPU-Grundlagen, Schlüsselparameter, Architekturentwicklung und leistungsbeeinflussende Faktoren, die professionelle Anleitung für die Grafikkartenauswahl und Leistungsoptimierung bietet.",
                "author": {
                    "@type": "Organization",
                    "name": "Toxic Mushroom Test Team"
                },
                "publisher": {
                    "@id": "https://volumeshaderbmtest.com/#organization"
                },
                "mainEntityOfPage": {
                    "@type": "WebPage",
                    "@id": "https://volumeshaderbmtest.com/#gpu-knowledge"
                },
                "datePublished": "2024-01-01",
                "dateModified": "2024-01-01"
            },
            {
                "@type": "FAQPage",
                "@id": "https://volumeshaderbmtest.com/#faq",
                "mainEntity": [
                    {
                        "@type": "Question",
                        "name": "Warum erwärmt sich mein Gerät während des Tests?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Das ist normal. Der Toxic Mushroom Test lässt die GPU mit voller Kapazität arbeiten, und Wärmeerzeugung ist unvermeidlich. Wenn die Temperatur zu hoch ist, wird empfohlen, den Test zu stoppen oder die Teststufe zu senken."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "Was bedeutet die Punktzahl in den Testergebnissen?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Die Punktzahl berücksichtigt FPS, Rendering-Stabilität und Teststufe. Eine höhere Punktzahl steht für eine stärkere GPU-Leistung und kann horizontal mit anderen Geräten verglichen werden."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "Was soll ich tun, wenn mein Browser anzeigt, dass WebGL nicht unterstützt wird?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Bitte stellen Sie sicher, dass Ihr Browser auf dem neuesten Stand ist, überprüfen Sie, ob die Hardwarebeschleunigung aktiviert ist, oder versuchen Sie, den Browser zu wechseln. Einige mobile Geräte unterstützen möglicherweise kein WebGL 2.0."
                        }
                    },
                    {
                        "@type": "Question",
                        "name": "Werden die Testdaten hochgeladen?",
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": "Alle Testdaten werden lokal gespeichert und nicht auf einen Server hochgeladen. Sie können Tests sicher durchführen, ohne sich Gedanken über Datenschutzprobleme machen zu müssen."
                        }
                    }
                ]
            },
            {
                "@type": "ItemList",
                "@id": "https://volumeshaderbmtest.com/#gpu-recommendations",
                "name": "Grafikkartenempfehlungsleitfaden",
                "description": "Professionelle Grafikkartenempfehlungen nach Budget und Zweck kategorisiert",
                "itemListElement": [
                    {
                        "@type": "Product",
                        "position": 1,
                        "name": "NVIDIA GeForce RTX 4060",
                        "description": "Einstiegs-Gaming-Grafikkarte, geeignet für 1080p-Gaming in hoher Qualität",
                        "category": "Grafikkarte",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1500",
                            "highPrice": "2000",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 2,
                        "name": "AMD Radeon RX 7600",
                        "description": "Beste Preis-Leistungs-Wahl, empfohlene Einstiegs-Grafikkarte",
                        "category": "Grafikkarte",
                        "brand": {
                            "@type": "Brand",
                            "name": "AMD"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "1400",
                            "highPrice": "1800",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 3,
                        "name": "NVIDIA GeForce RTX 4070",
                        "description": "Mittelklasse-Gaming-Grafikkarte, unterstützt 1440p-Gaming in hoher Qualität",
                        "category": "Grafikkarte",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "3000",
                            "highPrice": "3500",
                            "availability": "https://schema.org/InStock"
                        }
                    },
                    {
                        "@type": "Product",
                        "position": 4,
                        "name": "NVIDIA GeForce RTX 4090",
                        "description": "Flaggschiff-Grafikkarte, ultimative 4K-Gaming-Leistung",
                        "category": "Grafikkarte",
                        "brand": {
                            "@type": "Brand",
                            "name": "NVIDIA"
                        },
                        "offers": {
                            "@type": "AggregateOffer",
                            "priceCurrency": "CNY",
                            "lowPrice": "12000",
                            "highPrice": "15000",
                            "availability": "https://schema.org/InStock"
                        }
                    }
                ]
            },
            {
                "@type": "HowTo",
                "@id": "https://volumeshaderbmtest.com/#test-guide",
                "name": "GPU-Leistungstestanleitung",
                "description": "Detaillierte GPU-Leistungstestschritte und Best Practices",
                "image": "https://volumeshaderbmtest.com/images/test-guide.jpg",
                "totalTime": "PT5M",
                "supply": [
                    {
                        "@type": "HowToSupply",
                        "name": "WebGL-kompatibler Browser"
                    },
                    {
                        "@type": "HowToSupply", 
                        "name": "Dedizierte oder integrierte Grafikkarte"
                    }
                ],
                "tool": [
                    {
                        "@type": "HowToTool",
                        "name": "Volume Shader BM Testtool"
                    }
                ],
                "step": [
                    {
                        "@type": "HowToStep",
                        "position": 1,
                        "name": "Vorbereitung vor dem Test",
                        "text": "Schließen Sie unnötige Programme und Browser-Tabs, sorgen Sie für eine gute Wärmeableitungsumgebung des Geräts"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 2,
                        "name": "Testmodus auswählen",
                        "text": "Wählen Sie einen geeigneten Testmodus entsprechend der Geräteleistung, empfohlen wird, mit dem leichten Modus zu beginnen"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 3,
                        "name": "Test starten",
                        "text": "Klicken Sie auf Test starten und beobachten Sie die Echtzeit-Leistungsdatenänderungen"
                    },
                    {
                        "@type": "HowToStep",
                        "position": 4,
                        "name": "Ergebnisse anzeigen",
                        "text": "Sehen Sie sich nach Abschluss des Tests detaillierte Berichte und Leistungsempfehlungen an"
                    }
                ]
            }
        ]
    }
    </script>
    
    <script src="../static/js/js.js"></script>
    <script src="../static/js/chart.js"></script>
    <script src="../static/js/three.min.js"></script>
    <link rel="stylesheet" href="../static/css/all.min.css">
    <link href="../static/css/css2.css" rel="stylesheet">
    <link rel="stylesheet" href="../static/css/style.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                container: {
                    center: true,
                    padding: '1rem',
                    screens: {
                        sm: '640px',
                        md: '768px',
                        lg: '1024px',
                        xl: '1100px',
                        '2xl': '1100px'
                    },
                },
                extend: {
                    colors: {
                        primary: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            200: '#c7d2fe',
                            300: '#a5b4fc',
                            400: '#818cf8',
                            500: '#6366f1',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3',
                            900: '#312e81',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981',
                    },
                    fontFamily: {
                        sans: ['Noto Sans SC', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen font-sans text-gray-800 dark:text-gray-200">
    <!-- Background Decoration -->
    <div class="fixed inset-0 -z-10 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-200/30 via-transparent to-transparent dark:from-indigo-900/20"></div>
    
    <!-- Header Navigation Bar -->
    <header class="sticky top-0 z-50 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <img src="../logo.svg" alt="Logo" class="w-8 h-8">
                <h1 class="text-xl font-semibold">Volume Shader BM Test</h1>
            </div>
            <nav class="nav-menu hidden md:block">
                <ul class="flex flex-wrap gap-x-6 gap-y-2">
                    <li><a href="#test" class="active nav-link">Leistungstest</a></li>
                    <li><a href="#about" class="nav-link">Über das Tool</a></li>
                    <li><a href="#test-guide" class="nav-link">Testanleitung</a></li>
                    <li><a href="#technology" class="nav-link">Technische Prinzipien</a></li>
                    <!-- <li><a href="#compatibility" class="nav-link">Kompatibilität</a></li>
                    <li><a href="#gpu-knowledge" class="nav-link">GPU-Wissen</a></li>
                    <li><a href="#hardware-recommendation" class="nav-link">Hardware-Empfehlungen</a></li> -->
                    <li><a href="#faq" class="nav-link">FAQ</a></li>
                </ul>
            </nav>
            <div class="flex items-center space-x-4">
                <div class="language-selector relative">
                    <button class="flex items-center space-x-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="language-toggle" title="Sprache wechseln">
                        <i class="fas fa-globe"></i>
                        <span class="text-sm">Deutsch</span>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </button>
                    <div class="language-dropdown absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg overflow-hidden z-20 hidden">
                        <a href="../index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">Englisch</a>
                        <a href="../zh-cn/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">中文</a>
                        <a href="../ja-jp/index.html" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">日本語</a>
                    </div>
                </div>
                <div class="theme-toggle rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="theme-toggle" title="Design wechseln">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="mobile-menu-btn md:hidden rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 transition-colors" id="mobile-menu-btn" title="Menü">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
        <!-- Mobile Navigation Menu (Hidden by default) -->
        <div class="mobile-nav hidden bg-white dark:bg-gray-900 shadow-md w-full absolute left-0 top-full border-t border-gray-200 dark:border-gray-700 py-3 px-4 md:hidden">
            <ul class="flex flex-col space-y-3">
                <li><a href="#test" class="active block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Leistungstest</a></li>
                <li><a href="#about" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Über das Tool</a></li>
                <li><a href="#test-guide" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Testanleitung</a></li>
                <li><a href="#technology" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Technische Prinzipien</a></li>
                <li><a href="#compatibility" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Kompatibilität</a></li>
                <li><a href="#gpu-knowledge" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">GPU-Wissen</a></li> -->
                <li><a href="#hardware-recommendation" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">Hardware-Empfehlungen</a></li>
                <li><a href="#faq" class="block py-2 px-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">FAQ</a></li>
            </ul>
        </div>
    </header>
    
    <!-- Main Content Area -->
    <div class="container">
          
        <!-- Main Test Area -->
        <div id="test" class="card p-6 mb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 3D Rendering Area -->
                <div class="lg:col-span-2">
                    <div class="mb-4 flex justify-between items-center">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Volume Shader BM Test</h2>
                        <div class="flex space-x-2">
                            <button id="fullscreen-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-expand mr-1"></i> Vollbild
                            </button>
                            <button id="screenshot-btn" class="btn-secondary text-sm px-3 py-1">
                                <i class="fas fa-camera mr-1"></i> Screenshot
                            </button>
                        </div>
                    </div>
                    
                    <!-- WebGL Rendering Canvas -->
                    <div id="canvas-container" class="relative bg-black rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700" style="height: 400px;">
                        <canvas id="mushroom-canvas" class="w-full h-full"></canvas>
                        
                        <!-- Rendering Status Overlay -->
                        <div id="render-overlay" class="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                            <div class="text-center text-white">
                                <i class="fas fa-cube text-4xl mb-2 animate-pulse"></i>
                                <div class="hidden lg:block">Klicken Sie auf die Schaltfläche Test starten, um mit dem Rendering zu beginnen</div>
                                <div class="lg:hidden">Klicken Sie auf die Schaltfläche unten, um den GPU-Test zu starten</div>
                                <div class="text-sm text-gray-300 mt-1">Stellen Sie sicher, dass Ihr Gerät WebGL unterstützt</div>
                            </div>
                        </div>
                        
                        <!-- Mobile Test Controls Overlay -->
                        <div id="mobile-controls-overlay" class="absolute inset-0 bg-black/80 backdrop-blur-sm lg:hidden" style="display: none;">
                            <div class="flex flex-col h-full">
                                <!-- Close Button -->
                                <div class="flex justify-end p-3 flex-shrink-0">
                                    <button id="close-mobile-controls" class="text-white hover:text-gray-300 text-xl">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                
                                <!-- Content Area -->
                                <div class="flex-1 flex flex-col overflow-hidden">
                                    <!-- Title -->
                                    <div class="px-4 pb-3 flex-shrink-0">
                                        <h3 class="text-white font-semibold text-lg text-center">Testmodus auswählen</h3>
                                    </div>
                                    
                                    <!-- Test Mode Selection - Scrollable Area -->
                                    <div class="flex-1 overflow-y-auto px-4">
                                        <div class="space-y-3">
                                            <div class="mobile-test-mode-card active" data-mode="light">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-blue-400 text-xl">
                                                        <i class="fas fa-feather"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Leichter Test</h4>
                                                        <p class="text-gray-300 text-sm">Grundlegender Leistungstest, geeignet für Geräte mit niedriger Leistung</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="medium">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-yellow-400 text-xl">
                                                        <i class="fas fa-shield-alt"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Mittlerer Test</h4>
                                                        <p class="text-gray-300 text-sm">Standardlasttest, geeignet für Geräte der Mittelklasse</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="heavy">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-orange-400 text-xl">
                                                        <i class="fas fa-fire"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Schwerer Test</h4>
                                                        <p class="text-gray-300 text-sm">Hochintensiver Test, geeignet für High-End-Geräte</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="mobile-test-mode-card" data-mode="extreme">
                                                <div class="flex items-center space-x-3 p-3 rounded-lg bg-white/10 border border-white/20">
                                                    <div class="text-red-400 text-xl">
                                                        <i class="fas fa-bomb"></i>
                                                    </div>
                                                    <div class="flex-1">
                                                        <h4 class="text-white font-medium">Extremer Test</h4>
                                                        <p class="text-gray-300 text-sm">Extremer Stresstest, mit Vorsicht zu verwenden</p>
                                                    </div>
                                                    <div class="text-white">
                                                        <i class="fas fa-check-circle hidden"></i>
                                                        <i class="far fa-circle"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Control Buttons - Fixed at Bottom -->
                                    <div class="p-4 flex-shrink-0 space-y-3">
                                        <button id="mobile-start-test-btn" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                            <i class="fas fa-play mr-2"></i>
                                            Test starten
                                        </button>
                                        
                                        <button id="mobile-stop-test-btn" class="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-all duration-200 flex items-center justify-center opacity-50 shadow-lg" disabled>
                                            <i class="fas fa-stop mr-2"></i>
                                            Test stoppen
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mobile Quick Start Button -->
                        <div id="mobile-quick-start" class="absolute bottom-4 left-4 right-4 lg:hidden">
                            <button id="show-mobile-controls" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg">
                                <i class="fas fa-play mr-2"></i>
                                GPU-Test starten
                            </button>
                        </div>
                        
                        <!-- FPS and Performance Information Overlay -->
                        <div id="performance-hud" class="absolute top-2 left-2 bg-black/70 backdrop-blur-sm rounded-lg p-2 text-white text-sm font-mono hidden">
                            <div class="grid grid-cols-2 gap-x-4 gap-y-1">
                                <div>FPS: <span id="fps-display" class="text-green-400">0</span></div>
                                <div>Renderzeit: <span id="render-time" class="text-blue-400">0ms</span></div>
                                <div>Dreiecke: <span id="triangle-count" class="text-yellow-400">0</span></div>
                                <div>Komplexität: <span id="complexity-level" class="text-purple-400">1.0x</span></div>
                            </div>
                        </div>
                        
                        <!-- Warning Message -->
                        <div id="warning-overlay" class="absolute bottom-2 left-2 right-2 bg-red-600/90 backdrop-blur-sm rounded-lg p-2 text-white text-sm hidden">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            <span id="warning-text">Leistungsproblem erkannt, erwägen Sie, die Teststufe zu senken</span>
                        </div>
                    </div>
                    
                    <!-- Device Information -->
                    <div class="mt-4 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Geräteinformationen</h3>
                        <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex justify-between">
                                <span>GPU:</span>
                                <span id="gpu-info" class="text-right">Wird erkannt...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>WebGL-Version:</span>
                                <span id="webgl-version" class="text-right">Wird erkannt...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Browser:</span>
                                <span id="browser-info" class="text-right">Wird erkannt...</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Betriebssystem:</span>
                                <span id="os-info" class="text-right">Wird erkannt...</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Control Panel and Results Area -->
                <div class="space-y-4 hidden lg:block">
                    <!-- Test Mode Selection Area -->
                    <div class="test-modes-container">
                            <a href="#" class="test-mode-card active" data-mode="light">
                                <div class="mode-icon">
                                    <i class="fas fa-feather"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Leichter Test</h3>
                                    <p>Grundlegender Leistungstest</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="medium">
                                <div class="mode-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Mittlerer Test</h3>
                                    <p>Standardlasttest</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="heavy">
                                <div class="mode-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Schwerer Test</h3>
                                    <p>Hochintensiver Test</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="extreme">
                                <div class="mode-icon">
                                    <i class="fas fa-bomb"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Extremer Test</h3>
                                    <p>Extremer Stresstest</p>
                                </div>
                            </a>
                            <a href="#" class="test-mode-card" data-mode="custom">
                                <div class="mode-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="mode-info">
                                    <h3>Benutzerdefiniert</h3>
                                    <p>Benutzerdefinierter Parametertest</p>
                                </div>
                            </a>
                        </div>
                    
                    <!-- Test Controls -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Teststeuerung</h3>
                        
                        <!-- Test Buttons -->
                        <div class="grid grid-cols-2 gap-2 mb-4">
                            <button id="start-test-btn" class="btn-primary flex items-center justify-center h-10">
                                <i class="fas fa-play mr-2"></i> Test starten
                            </button>
                            <button id="stop-test-btn" class="btn-danger flex items-center justify-center h-10 opacity-50" disabled>
                                <i class="fas fa-stop mr-2"></i> Test stoppen
                            </button>
                        </div>
                        
                        <!-- Test Progress -->
                        <div id="test-progress" class="hidden mb-4">
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>Testfortschritt</span>
                                <span id="progress-text">0/60 Sekunden</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- Custom Parameters -->
                        <div id="custom-params" class="space-y-3 hidden">
                            <div>
                                <label for="triangle-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dreiecksanzahl</label>
                                <input type="range" id="triangle-slider" min="1000" max="500000" value="50000" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Aktuell: <span id="triangle-value">50.000</span></div>
                            </div>
                            <div>
                                <label for="complexity-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Shader-Komplexität</label>
                                <input type="range" id="complexity-slider" min="0.5" max="10" step="0.5" value="2.5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Aktuell: <span id="complexity-value">2,5x</span></div>
                            </div>
                            <div>
                                <label for="duration-slider" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Testdauer (Sekunden)</label>
                                <input type="range" id="duration-slider" min="10" max="300" value="60" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Aktuell: <span id="duration-value">60</span> Sekunden</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Current Performance Metrics -->
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Echtzeitleistung</h3>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="current-fps">0</div>
                                <div class="text-gray-500 dark:text-gray-400">FPS</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="current-score">0</div>
                                <div class="text-gray-500 dark:text-gray-400">Punktzahl</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-yellow-600 dark:text-yellow-400" id="current-temp">Normal</div>
                                <div class="text-gray-500 dark:text-gray-400">Temperaturstatus</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-medium text-purple-600 dark:text-purple-400" id="current-level">Leicht</div>
                                <div class="text-gray-500 dark:text-gray-400">Teststufe</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="grid grid-cols-2 gap-2">
                        <button id="share-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-share-alt mr-2"></i> Ergebnisse teilen
                        </button>
                        <button id="history-btn" class="btn-secondary flex items-center justify-center h-10">
                            <i class="fas fa-history mr-2"></i> Testverlauf
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Performance Charts and Test History -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Performance Chart Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Leistungsanalyse</h2>
                    <div class="flex space-x-2">
                        <button class="chart-tab active" data-chart="fps">FPS-Diagramm</button>
                        <button class="chart-tab" data-chart="score">Punktzahltrend</button>
                    </div>
                </div>
                <div class="chart-content flex-grow">
                    <div class="chart-wrapper h-full" id="fps-chart-wrapper" style="display:block;">
                        <canvas id="fpsChart" class="w-full h-full"></canvas>
                    </div>
                    <div class="chart-wrapper h-full" id="score-chart-wrapper" style="display:none;">
                        <canvas id="scoreChart" class="w-full h-full"></canvas>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2 text-center">
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Durchschn. FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="avg-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Maximum FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="max-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Minimum FPS</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="min-fps">-</div>
                    </div>
                    <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-2">
                        <div class="text-sm text-gray-600 dark:text-gray-400">Stabilität</div>
                        <div class="font-semibold text-gray-800 dark:text-gray-200" id="stability">-</div>
                    </div>
                </div>
            </div>

            <!-- Test History Area -->
            <div class="card p-6 mb-0 flex flex-col" style="height: 400px;">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">Testverlauf</h2>
                    <button class="clear-history-btn btn-danger text-sm py-1 px-3">
                        <i class="fas fa-trash-alt mr-1"></i> Löschen
                    </button>
                </div>
                <div class="history-log flex-grow overflow-y-auto bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="empty-log p-4 text-center text-gray-500 dark:text-gray-400 flex flex-col items-center justify-center h-full">
                        <i class="fas fa-chart-line text-4xl mb-2 opacity-30"></i>
                        <p>Noch keine Testaufzeichnungen</p>
                        <p class="text-sm">Abgeschlossene Leistungstests werden hier angezeigt</p>
                    </div>
                    <div id="history-items" class="hidden max-h-full overflow-y-auto">
                        <!-- History items will be dynamically added via JavaScript -->
                    </div>
                </div>
                <div class="mt-4 flex justify-between">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        Insgesamt <span id="history-count">0</span> Tests
                    </div>
                    <div class="flex space-x-2">
                        <button id="export-history" class="btn-primary text-sm py-1 px-3 flex items-center">
                            <i class="fas fa-download mr-1"></i> Exportieren
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Volume Shader BM Introduction Section -->
        <div class="card p-6 mt-6 overflow-hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                <div class="order-2 md:order-1">
                    <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Entdecken Sie Volume Shader BM</h2>
                    <div class="text-gray-700 dark:text-gray-300 space-y-4">
                        <p>Volume Shader BM ist ein hochmodernes GPU-Benchmark-Tool, das entwickelt wurde, um Ihre Grafikhardware durch komplexes volumetrisches Shader-Rendering an ihre Grenzen zu bringen.</p>
                        <p>Unser Benchmark nutzt fortschrittliche 3D-Volumen-Shader, um realistische Fraktalmuster zu erzeugen, die die Verarbeitungsfähigkeiten Ihrer GPU auf die Probe stellen und genaue Leistungskennzahlen liefern.</p>
                        <p>Ob Sie ein Gamer sind, der die Leistung seines Systems bewerten möchte, ein Profi, der Hardwarefähigkeiten validieren muss, oder einfach neugierig auf die grafischen Fähigkeiten Ihres Geräts sind, Volume Shader BM liefert präzise und zuverlässige Ergebnisse.</p>
                        <div class="mt-6">
                            <a href="#test" class="btn-primary inline-flex items-center px-4 py-2 rounded-md">
                                <i class="fas fa-play mr-2"></i> Benchmark starten
                            </a>
                        </div>
                    </div>
                </div>
                <div class="order-1 md:order-2">
                    <div class="rounded-lg overflow-hidden shadow-lg transform transition-transform hover:scale-105 duration-300">
                        <img src="../images/og-image.avif" alt="Volume Shader Benchmark Visualization" class="w-full h-auto">
                    </div>
                </div>
            </div>
        </div>

        <!-- About Tool Area -->
        <div id="about" class="card p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Über Volume Shader BM Test</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Volume Shader BM Test ist ein WebGL-basiertes Online-GPU-Leistungsbewertungstool, das die Grafikleistung Ihres Geräts durch das Rendern komplexer 3D-Volumen-Shader bewertet.</p>
                <div class="bg-white/50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2">Beschreibung der Teststufen</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-sm">Leicht: 10K Dreiecke</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-sm">Mittel: 50K Dreiecke</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-orange-500 mr-2"></span>
                            <span class="text-sm">Schwer: 200K Dreiecke</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-sm">Extrem: 500K Dreiecke</span>
                        </div>
                    </div>
                </div>
                <p><strong>Hinweis:</strong> Tests mit hoher Intensität können bei Geräten mit niedriger Leistung zu Verzögerungen oder Erwärmung führen. Bitte wählen Sie eine angemessene Teststufe basierend auf der Leistung Ihres Geräts.</p>
            </div>
        </div>

        <!-- Test Guide Area -->
        <div id="test-guide" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-book-open mr-2 text-primary-500"></i>GPU-Testanleitung
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Test Preparation -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-clipboard-check mr-2 text-green-500"></i>Testvorbereitung
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Schließen Sie unnötige Programme und Browser-Tabs, um Systemressourcen freizugeben</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Stellen Sie sicher, dass Ihr Gerät gute Kühlbedingungen hat, um Überhitzung während des Tests zu vermeiden</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Verwenden Sie eine stabile Stromversorgung, um Unterbrechungen während des Tests zu vermeiden</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mr-2 mt-1 text-sm"></i>
                                <span>Aktualisieren Sie Ihre Grafiktreiber auf die neueste Version für optimale Leistung</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- Test Steps -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center">
                        <i class="fas fa-list-ol mr-2 text-blue-500"></i>Testschritte
                    </h3>
                    <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <ol class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">1</span>
                                <span>Wählen Sie einen geeigneten Testmodus (empfohlen wird, mit Leicht zu beginnen)</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">2</span>
                                <span>Passen Sie bei Bedarf Komplexität und Testdauer an</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">3</span>
                                <span>Klicken Sie auf Test starten und beobachten Sie die Echtzeit-Leistungsdaten</span>
                            </li>
                            <li class="flex items-start">
                                <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-3 mt-0.5">4</span>
                                <span>Sehen Sie sich nach Abschluss des Tests den detaillierten Bericht und die Empfehlungen an</span>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- Test Mode Details -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-layer-group mr-2 text-purple-500"></i>Details zu den Testmodi
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                            <h4 class="font-medium text-green-800 dark:text-green-200">Leichter Test</h4>
                        </div>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• Geeignet für Bürogeräte und Einsteiger-GPUs</li>
                            <li>• 10K Dreiecke, geringe GPU-Last</li>
                            <li>• Testdauer: 30 Sekunden</li>
                            <li>• Geringe Wärmeentwicklung, geeignet für längere Läufe</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
                            <h4 class="font-medium text-blue-800 dark:text-blue-200">Mittlerer Test</h4>
                        </div>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• Geeignet für Mittelklasse-Gaming-GPUs</li>
                            <li>• 50K Dreiecke, mittlere GPU-Last</li>
                            <li>• Testdauer: 60 Sekunden</li>
                            <li>• Kann Leistungsengpässe und Stabilität erkennen</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200">Schwerer Test</h4>
                        </div>
                        <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                            <li>• Geeignet für High-End-Gaming-GPUs</li>
                            <li>• 200K Dreiecke, hohe GPU-Last</li>
                            <li>• Testdauer: 90 Sekunden</li>
                            <li>• Testet Kühlsystem und Netzteilstabilität</li>
                        </ul>
                    </div>
                    
                    <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-3">
                            <span class="w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                            <h4 class="font-medium text-red-800 dark:text-red-200">Extremer Test</h4>
                        </div>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• Geeignet für professionelle High-End-GPUs</li>
                            <li>• 500K Dreiecke, extreme GPU-Last</li>
                            <li>• Testdauer: 120 Sekunden</li>
                            <li>• Stresstest, kann zur Geräteerwärmung führen</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Results Interpretation Guide -->
            <div class="mt-6">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-line mr-2 text-indigo-500"></i>Leitfaden zur Ergebnisinterpretation
                </h3>
                <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-indigo-200 dark:border-indigo-700">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium mb-2 text-indigo-800 dark:text-indigo-200">FPS-Analyse</h4>
                            <ul class="text-sm text-indigo-700 dark:text-indigo-300 space-y-1">
                                <li>• <strong>60+ FPS</strong>: Ausgezeichnete Leistung</li>
                                <li>• <strong>30-60 FPS</strong>: Gute Leistung</li>
                                <li>• <strong>15-30 FPS</strong>: Durchschnittliche Leistung</li>
                                <li>• <strong>&lt;15 FPS</strong>: Niedrige Leistung</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-purple-800 dark:text-purple-200">Temperaturstatus</h4>
                            <ul class="text-sm text-purple-700 dark:text-purple-300 space-y-1">
                                <li>• <strong>Normal</strong>: GPU-Temperatur stabil</li>
                                <li>• <strong>Warm</strong>: Leichter Temperaturanstieg, normaler Bereich</li>
                                <li>• <strong>Heiß</strong>: Kühlung erforderlich</li>
                                <li>• <strong>Überhitzung</strong>: Empfehlung, Test zu stoppen</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2 text-pink-800 dark:text-pink-200">Gesamtpunktzahl</h4>
                            <ul class="text-sm text-pink-700 dark:text-pink-300 space-y-1">
                                <li>• <strong>9000+</strong>: Flaggschiff-Leistung</li>
                                <li>• <strong>6000-9000</strong>: High-End-Leistung</li>
                                <li>• <strong>3000-6000</strong>: Mittelklasse-Leistung</li>
                                <li>• <strong>&lt;3000</strong>: Einstiegsklasse-Leistung</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Important Notes -->
            <div class="mt-6">
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Wichtige Hinweise</h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Achten Sie auf Batteriestand und Wärme, wenn Sie Tests mit hoher Intensität auf mobilen Geräten durchführen</li>
                                <li>• Wenn während des Tests Bildschirmartefakte oder Systemabstürze auftreten, stoppen Sie den Test sofort</li>
                                <li>• Testergebnisse dienen nur als Referenz; die tatsächliche Spielleistung wird auch von CPU, Arbeitsspeicher und anderen Faktoren beeinflusst</li>
                                <li>• Regelmäßige Tests können helfen, Änderungen der GPU-Leistung zu überwachen und Hardwareprobleme frühzeitig zu erkennen</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Principles Area -->
        <div id="technology" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Technische Prinzipien</h2>
            <div class="text-gray-700 dark:text-gray-300 space-y-4">
                <p>Dieses Tool verwendet fortschrittliche WebGL 2.0-Technologie und Volumen-Shader-Algorithmen, um GPU-Leistungstests durch folgende Techniken zu implementieren:</p>
                <ul class="list-disc pl-5 space-y-2 marker:text-primary-500 dark:marker:text-primary-400">
                    <li><strong>Volumen-Rendering-Technologie</strong>: Verwendet komplexe mathematische Funktionen zur Erzeugung von 3D-Volumentexturen, die Form und Textur von giftigen Pilzen simulieren</li>
                    <li><strong>Dynamische Shader</strong>: Passt die Komplexität des Fragment-Shaders dynamisch basierend auf der Teststufe an und erhöht die GPU-Rechenlast</li>
                    <li><strong>Echtzeit-Leistungsüberwachung</strong>: Überwacht wichtige Kennzahlen wie FPS und Renderzeit durch WebGL-Erweiterungen und Performance-API</li>
                    <li><strong>Temperatur-Inferenz-Algorithmus</strong>: Leitet den Temperaturstatus des Geräts durch Leistungsabfallkurven ab</li>
                </ul>
                <p>Der Testalgorithmus erhöht schrittweise die Rendering-Komplexität, bis er die Leistungsgrenze des Geräts erreicht und so die Verarbeitungsfähigkeit der GPU genau bewertet.</p>
            </div>
        </div>

        <!-- Compatibility Area -->
        <div id="compatibility" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Browser-Kompatibilität</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-chrome text-2xl text-yellow-600 dark:text-yellow-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Chrome</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Vollständig unterstützt. Neueste Version für beste Leistung empfohlen.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-firefox text-2xl text-orange-600 dark:text-orange-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Firefox</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Vollständig unterstützt mit ausgezeichneter Leistung.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-safari text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Safari</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Grundlegende Unterstützung, einige erweiterte Funktionen können eingeschränkt sein.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <i class="fab fa-edge text-2xl text-blue-600 dark:text-blue-400 mb-3"></i>
                    <h3 class="font-medium mb-2">Edge</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Vollständig unterstützt, Leistung ähnlich wie Chrome.</p>
                </div>
            </div>
        </div>

        <!-- GPU Knowledge Base Area -->
        <div id="gpu-knowledge" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-graduation-cap mr-2 text-primary-500"></i>GPU-Wissensdatenbank
            </h2>
            
            <!-- GPU Basic Knowledge -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-microchip mr-2 text-blue-500"></i>GPU-Grundlagen
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                        <h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Was ist eine GPU</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300">Graphics Processing Unit (Grafikprozessor), ein Chip, der auf die Verarbeitung von Grafikrendering und paralleles Computing spezialisiert ist, mit Tausenden kleiner Kerne, die hervorragend darin sind, viele einfache Aufgaben gleichzeitig zu bewältigen.</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">GPU vs CPU</h4>
                        <p class="text-sm text-green-700 dark:text-green-300">CPUs haben weniger, aber leistungsstärkere Kerne, geeignet für komplexe logische Operationen; GPUs haben viele einfache Kerne, ideal für paralleles Computing und Grafikrendering. Sie arbeiten zusammen für optimale Leistung.</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                        <h4 class="font-medium text-purple-800 dark:text-purple-200 mb-2">GPU-Typen</h4>
                        <p class="text-sm text-purple-700 dark:text-purple-300">Integrierte GPUs: In die CPU eingebaut, niedriger Stromverbrauch aber begrenzte Leistung; Dedizierte GPUs: Separate GPU-Chips, leistungsstarke Performance, geeignet für Gaming und professionelle Arbeit.</p>
                    </div>
                </div>
            </div>
            
            <!-- Key Parameters Analysis -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sliders-h mr-2 text-orange-500"></i>Analyse der Schlüsselparameter
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-memory text-red-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Videospeicher (VRAM)</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Dedizierter Speicher zum Speichern von Grafikdaten</li>
                                <li>• Kapazität beeinflusst die Gaming-Leistung bei hoher Auflösung</li>
                                <li>• 8GB+ empfohlen für Mainstream-Spiele</li>
                                <li>• 12GB+ benötigt für 4K-Gaming oder professionelle Arbeit</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-tachometer-alt text-blue-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Kerntakt</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Basistakt: Stabile Betriebsfrequenz der GPU</li>
                                <li>• Boost-Takt: Frequenz nach automatischem Übertakten</li>
                                <li>• Höhere Frequenz bedeutet stärkere Rechenleistung</li>
                                <li>• Kann durch Übertakten weiter verbessert werden</li>
                            </ul>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-server text-green-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Stream-Prozessoren</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Kerneinheiten, die paralleles Computing ausführen</li>
                                <li>• Mehr Einheiten bedeuten stärkere parallele Verarbeitungsfähigkeit</li>
                                <li>• Bei NVIDIA als CUDA-Kerne bezeichnet</li>
                                <li>• Bei AMD als Stream-Prozessoren (SP) bezeichnet</li>
                            </ul>
                        </div>
                        <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-road text-purple-500 mr-2"></i>
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">Busbreite & Bandbreite</h4>
                            </div>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li>• Speicherbusbreite beeinflusst die Datenübertragung</li>
                                <li>• 256-Bit und höher ist High-End-Konfiguration</li>
                                <li>• Bandbreite = Busbreite × Speicherfrequenz</li>
                                <li>• Hohe Bandbreite reduziert Leistungsengpässe</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- GPU Architecture Evolution -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-sitemap mr-2 text-indigo-500"></i>Entwicklung der GPU-Architektur
                </h3>
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-nvidia text-green-500 mr-2"></i>NVIDIA-Architektur
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ada Lovelace</strong></span>
                                    <span class="text-xs bg-green-100 dark:bg-green-800 px-2 py-1 rounded">RTX 40 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Ampere</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RTX 30 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Turing</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RTX 20 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>Pascal</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">GTX 10 Serie</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                                <i class="fab fa-amd text-red-500 mr-2"></i>AMD-Architektur
                            </h4>
                            <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 3</strong></span>
                                    <span class="text-xs bg-red-100 dark:bg-red-800 px-2 py-1 rounded">RX 7000 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA 2</strong></span>
                                    <span class="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">RX 6000 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>RDNA</strong></span>
                                    <span class="text-xs bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded">RX 5000 Serie</span>
                                </div>
                                <div class="flex justify-between items-center p-2 bg-white/50 dark:bg-gray-700/50 rounded">
                                    <span><strong>GCN</strong></span>
                                    <span class="text-xs bg-orange-100 dark:bg-orange-800 px-2 py-1 rounded">RX 500 Series</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Factors Affecting GPU Performance -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-chart-area mr-2 text-pink-500"></i>Faktoren, die die GPU-Leistung beeinflussen
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-thermometer-half text-3xl text-red-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Temperaturkontrolle</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Überhitzung verursacht Drosselung und beeinträchtigt die Leistung</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-bolt text-3xl text-yellow-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Stromversorgung</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Unzureichende Stromversorgung begrenzt die GPU-Leistung</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-download text-3xl text-blue-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Treiber</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Neueste Treiber optimieren die Spielleistung</p>
                    </div>
                    <div class="text-center bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <i class="fas fa-cogs text-3xl text-green-500 mb-3"></i>
                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Systemkonfiguration</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">CPU und RAM beeinflussen ebenfalls die Gesamtleistung</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hardware Recommendation Area -->
        <div id="hardware-recommendation" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                <i class="fas fa-award mr-2 text-primary-500"></i>GPU-Empfehlungsleitfaden
            </h2>
            
            <!-- Budget-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-500"></i>Budgetbasierte Empfehlungen
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Entry Level -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-5 border border-green-200 dark:border-green-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-200 text-lg">Einstiegsklasse</h4>
                            <!-- <p class="text-sm text-green-600 dark:text-green-400">￥1000-2000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4060</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">1080p Gaming in hoher Qualität</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">Empfohlen ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                     <i class="fas fa-shopping-cart mr-1"></i>Kaufen ansehen
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7600</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Beste Preis-Leistungs-Wahl</div>
                                 <div class="text-xs text-green-600 dark:text-green-400 mt-1">Empfohlen ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-green-500 hover:bg-green-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                     <i class="fas fa-shopping-cart mr-1"></i>Kaufen ansehen
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-green-700 dark:text-green-300">
                            <p><strong>Geeignet für:</strong> 1080p-Gaming, leichte Content-Erstellung, tägliche Büroarbeit</p>
                        </div>
                    </div>
                    
                    <!-- Mid-Range -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-5 border border-blue-200 dark:border-blue-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200 text-lg">Mittelklasse</h4>
                            <!-- <p class="text-sm text-blue-600 dark:text-blue-400">￥2000-4000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4070</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">1440p hohe Qualität</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Empfohlen ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                     <i class="fas fa-shopping-cart mr-1"></i>Kaufen ansehen
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7700 XT</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Großer VRAM-Vorteil</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">Empfohlen ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                     <i class="fas fa-shopping-cart mr-1"></i>Kaufen ansehen
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-blue-700 dark:text-blue-300">
                            <p><strong>Geeignet für:</strong> 1440p-Gaming, Videobearbeitung, Live-Streaming</p>
                        </div>
                    </div>
                    
                    <!-- High-End -->
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-5 border border-purple-200 dark:border-purple-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200 text-lg">High-End</h4>
                            <!-- <p class="text-sm text-purple-600 dark:text-purple-400">￥4000-8000</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">4K-Gaming-Kraftpaket</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Empfohlen ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                     <i class="fas fa-shopping-cart mr-1"></i>Kaufen ansehen
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RX 7900 XTX</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">24GB großer VRAM</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400 mt-1">Empfohlen ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-xtx">
                                     <i class="fas fa-shopping-cart mr-1"></i>Kaufen ansehen
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-purple-700 dark:text-purple-300">
                            <p><strong>Geeignet für:</strong> 4K-Gaming, professionelles Rendering, KI-Computing</p>
                        </div>
                    </div>
                    
                    <!-- Flagship -->
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg p-5 border border-orange-200 dark:border-orange-700">
                        <div class="text-center mb-4">
                            <h4 class="font-semibold text-orange-800 dark:text-orange-200 text-lg">Flaggschiff</h4>
                            <!-- <p class="text-sm text-orange-600 dark:text-orange-400">￥8000+</p> -->
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4090</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Ultimative Leistung</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">Empfohlen ⭐⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                     <i class="fas fa-shopping-cart mr-1"></i>Kaufen ansehen
                                 </a>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-800/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">RTX 4080 Super</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">Preiswerteres Flaggschiff</div>
                                 <div class="text-xs text-orange-600 dark:text-orange-400 mt-1">Empfohlen ⭐⭐⭐⭐</div>
                                 <a href="#" class="purchase-btn mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080-super">
                                     <i class="fas fa-shopping-cart mr-1"></i>Kaufen ansehen
                                 </a>
                             </div>
                         </div>
                        <div class="mt-4 text-xs text-orange-700 dark:text-orange-300">
                            <p><strong>Geeignet für:</strong> 8K-Gaming, Workstations, Enthusiasten</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Usage-Based Recommendations -->
            <div class="mb-8">
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-tasks mr-2 text-blue-500"></i>Nutzungsbasierte Empfehlungen
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Gaming GPUs -->
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-6 border border-red-200 dark:border-red-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-gamepad text-3xl text-red-500 mr-3"></i>
                            <h4 class="font-semibold text-red-800 dark:text-red-200">Gaming-GPUs</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Esports-Gaming</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060, RX 7600</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">Hohe FPS 1080p</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4060
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7600">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7600
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">AAA-Titel</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7700 XT</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">1440p hohe Qualität</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7700-xt">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7700 XT
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">4K-Gaming</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-red-600 dark:text-red-400">4K Ultra-Einstellungen</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Content Creation -->
                    <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-video text-3xl text-blue-500 mr-3"></i>
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200">Content-Erstellung</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Videobearbeitung</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, RX 7900 GRE</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">Hardware-Encodierungsbeschleunigung</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rx-7900-gre">
                                         <i class="fas fa-shopping-cart mr-1"></i>RX 7900 GRE
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">3D-Rendering</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4080, RTX 4090</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">CUDA/OptiX-Beschleunigung</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Live-Streaming</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4060 und höher</div>
                                 <div class="text-xs text-blue-600 dark:text-blue-400">NVENC-Encodierung</div>
                                 <a href="#" class="purchase-btn w-full mt-2 bg-blue-500 hover:bg-blue-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4060-plus">
                                     <i class="fas fa-shopping-cart mr-1"></i>RTX 4060 Serie ansehen
                                 </a>
                             </div>
                         </div>
                    </div>
                    
                    <!-- Professional Work -->
                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-briefcase text-3xl text-purple-500 mr-3"></i>
                            <h4 class="font-semibold text-purple-800 dark:text-purple-200">Professionelle Arbeit</h4>
                        </div>
                                                 <div class="space-y-3">
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">KI-Entwicklung</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, RTX 4080</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Hoher VRAM-Bedarf</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4080">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4080
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">CAD-Design</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4070, Quadro-Serie</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Professionelle Treiberoptimierung</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4070">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4070
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="quadro-series">
                                         <i class="fas fa-shopping-cart mr-1"></i>Quadro-Serie
                                     </a>
                                 </div>
                             </div>
                             <div class="bg-white/70 dark:bg-gray-700/50 rounded p-3">
                                 <div class="font-medium text-gray-800 dark:text-gray-200">Wissenschaftliches Rechnen</div>
                                 <div class="text-sm text-gray-600 dark:text-gray-400">RTX 4090, A5000</div>
                                 <div class="text-xs text-purple-600 dark:text-purple-400">Doppelte Präzision Fließkomma</div>
                                 <div class="flex gap-1 mt-2">
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="rtx-4090">
                                         <i class="fas fa-shopping-cart mr-1"></i>RTX 4090
                                     </a>
                                     <a href="#" class="purchase-btn flex-1 bg-purple-500 hover:bg-purple-600 text-white text-xs py-1 px-2 rounded transition-colors inline-block text-center" data-product="a5000">
                                         <i class="fas fa-shopping-cart mr-1"></i>A5000
                                     </a>
                                 </div>
                             </div>
                         </div>
                    </div>
                </div>
            </div>
            
            <!-- Purchase Advice -->
            <div>
                <h3 class="text-lg font-medium mb-4 text-gray-800 dark:text-gray-200 flex items-center">
                    <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>Kaufberatung
                </h3>
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                                <i class="fas fa-exclamation-circle text-yellow-600 dark:text-yellow-400 mr-2"></i>
                                Wichtige Überlegungen
                            </h4>
                            <ul class="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                                <li>• Ausreichende Netzteilleistung sicherstellen</li>
                                <li>• Gehäuseplatz und Kühlung überprüfen</li>
                                <li>• CPU-Leistungsabstimmung berücksichtigen</li>
                                <li>• Auf VRAM-Kapazitätsanforderungen achten</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center">
                                <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 mr-2"></i>
                                Beste Kaufzeit
                            </h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                                <li>• 3-6 Monate nach Produktneuerscheinung</li>
                                <li>• Black Friday und andere Shopping-Events</li>
                                <li>• Nach Krypto-Mining-Abschwüngen</li>
                                <li>• Rationale Beobachtung des Gebrauchtmarkts</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-red-800 dark:text-red-200 mb-3 flex items-center">
                                <i class="fas fa-shield-alt text-red-600 dark:text-red-400 mr-2"></i>
                                Markenauswahl
                            </h4>
                            <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                                <li>• ASUS ROG: Erste Wahl für High-End-Gamer</li>
                                <li>• MSI Gaming: Ausgewogenes Preis-Leistungs-Verhältnis</li>
                                <li>• GIGABYTE AORUS: Ausgezeichnete Kühlung</li>
                                <li>• Galax: Guter Wert für Einsteiger</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div id="faq" class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Häufig gestellte Fragen</h2>
            <div class="space-y-4">
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Warum erwärmt sich mein Gerät während des Tests?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Das ist normal. Der Toxic Mushroom Test bringt Ihre GPU dazu, mit voller Kapazität zu arbeiten, daher ist eine Wärmeerzeugung unvermeidlich. Wenn die Temperatur zu hoch wird, empfehlen wir, den Test zu stoppen oder die Teststufe zu senken.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Was bedeutet die Punktzahl im Testergebnis?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Die Punktzahl berücksichtigt FPS, Rendering-Stabilität und Teststufe. Eine höhere Punktzahl zeigt eine stärkere GPU-Leistung an und kann für den direkten Vergleich mit anderen Geräten verwendet werden.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Was tun, wenn mein Browser anzeigt, dass WebGL nicht unterstützt wird?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Bitte stellen Sie sicher, dass Ihr Browser auf dem neuesten Stand ist, überprüfen Sie, ob die Hardwarebeschleunigung aktiviert ist, oder versuchen Sie einen anderen Browser. Einige mobile Geräte unterstützen möglicherweise kein WebGL 2.0.</p>
                </div>
                <div class="bg-white/70 dark:bg-gray-800/70 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium mb-2 text-gray-800 dark:text-gray-200">Werden meine Testdaten hochgeladen?</h3>
                    <p class="text-gray-700 dark:text-gray-300">Alle Testdaten werden lokal gespeichert und nicht auf einen Server hochgeladen. Sie können Tests sicher durchführen, ohne sich um Datenschutzprobleme sorgen zu müssen.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200 dark:border-gray-800">
        <div class="container py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Volume Shader BM Test</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">Professionelles Online-GPU-Leistungstesttool</p>
                    <p class="text-gray-600 dark:text-gray-400">© 2025 <a href="">Volume Shader BM Test</a></p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Verwandte Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Benutzerhandbuch</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Technischer Support</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Rückmeldung</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Haftungsausschluss</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Dieses Tool dient ausschließlich zum Testen der GPU-Leistung. Geräteerwärmung und Stromverbrauch während der Nutzung sind normale Phänomene. Bitte wählen Sie eine angemessene Teststufe basierend auf den Fähigkeiten Ihres Geräts.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../static/js/app.js"></script>
    
    <!-- Sprachauswahl-Skript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const languageToggle = document.getElementById('language-toggle');
            const languageDropdown = document.querySelector('.language-dropdown');
            
            // Sprachauswahlmenü ein-/ausblenden
            languageToggle.addEventListener('click', function(e) {
                e.preventDefault();
                languageDropdown.classList.toggle('hidden');
            });
            
            // Dropdown-Menü schließen, wenn anderswo auf der Seite geklickt wird
            document.addEventListener('click', function(e) {
                if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                    languageDropdown.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>